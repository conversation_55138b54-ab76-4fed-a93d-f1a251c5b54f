
import { Link } from "react-router-dom";

export function Footer() {
  return (
    <footer className="bg-muted/50 border-t">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-primary-600 to-accent-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">ML</span>
              </div>
              <span className="font-semibold text-lg">Metamorphic Labs</span>
            </div>
            <p className="text-muted-foreground max-w-md">
              Engineering tomorrow's intelligence through cutting-edge AI systems architecture 
              and intelligent automation solutions.
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Navigation</h3>
            <ul className="space-y-2">
              <li><Link to="/about" className="text-muted-foreground hover:text-foreground">About</Link></li>
              <li><Link to="/expertise" className="text-muted-foreground hover:text-foreground">Expertise</Link></li>
              <li><Link to="/projects" className="text-muted-foreground hover:text-foreground">Projects</Link></li>
              <li><Link to="/contact" className="text-muted-foreground hover:text-foreground">Contact</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Services</h3>
            <ul className="space-y-2">
              <li><span className="text-muted-foreground">AI Platforms</span></li>
              <li><span className="text-muted-foreground">Intelligent Automation</span></li>
              <li><span className="text-muted-foreground">Custom Software</span></li>
            </ul>
          </div>
        </div>
        
        <div className="mt-8 pt-8 border-t border-border">
          <p className="text-center text-muted-foreground">
            © {new Date().getFullYear()} Metamorphic Labs. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
