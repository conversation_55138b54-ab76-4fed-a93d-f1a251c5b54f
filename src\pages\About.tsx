
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const About = () => {
  const timeline = [
    {
      year: "2024",
      title: "Metamorphic Labs Founded",
      description: "Established to bridge the gap between AI research and practical business applications"
    },
    {
      year: "2023",
      title: "AI Architecture Expertise",
      description: "Specialized in designing scalable AI systems for enterprise clients"
    },
    {
      year: "2022",
      title: "Full-Stack Innovation",
      description: "Expanded capabilities to include end-to-end software development"
    },
    {
      year: "2021",
      title: "Research & Development",
      description: "Deep research into machine learning and intelligent automation"
    }
  ];

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            About <span className="text-gradient">Metamorphic Labs</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            We are a forward-thinking technology company dedicated to creating intelligent solutions 
            that transform how businesses operate in the digital age.
          </p>
        </div>

        {/* Mission Section */}
        <section className="mb-20">
          <Card className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-950 dark:to-accent-950 border-0">
            <CardContent className="p-12">
              <h2 className="text-3xl font-bold mb-6 text-center">Our Mission</h2>
              <p className="text-lg text-muted-foreground text-center max-w-4xl mx-auto leading-relaxed">
                At Metamorphic Labs, we believe that artificial intelligence should be accessible, 
                practical, and transformative. Our mission is to engineer tomorrow's intelligence by 
                creating AI systems that don't just solve problems—they revolutionize entire industries. 
                We combine cutting-edge research with pragmatic engineering to deliver solutions that 
                scale with your business and adapt to your evolving needs.
              </p>
            </CardContent>
          </Card>
        </section>

        {/* Timeline */}
        <section className="mb-20">
          <h2 className="text-3xl font-bold text-center mb-12">Our Journey</h2>
          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-0.5 h-full bg-gradient-to-b from-primary-600 to-accent-500"></div>
            <div className="space-y-12">
              {timeline.map((item, index) => (
                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                  <div className={`w-full max-w-md ${index % 2 === 0 ? 'pr-8' : 'pl-8'}`}>
                    <Card className="relative animate-fade-in">
                      <CardContent className="p-6">
                        <div className="absolute top-6 -right-3 w-6 h-6 bg-primary-600 rounded-full border-4 border-background"></div>
                        <Badge variant="secondary" className="mb-2">{item.year}</Badge>
                        <h3 className="text-lg font-semibold mb-2">{item.title}</h3>
                        <p className="text-muted-foreground">{item.description}</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section>
          <h2 className="text-3xl font-bold text-center mb-12">Meet Our Team</h2>
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <Card className="group hover:shadow-lg transition-all duration-300">
              <CardContent className="p-8 text-center">
                <div className="w-24 h-24 bg-gradient-to-r from-primary-600 to-primary-700 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <span className="text-white font-bold text-2xl">ML</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Michael Laffin</h3>
                <p className="text-accent-600 font-medium mb-4">AI Systems Architect</p>
                <p className="text-muted-foreground">
                  Specializes in designing scalable AI infrastructure and machine learning systems. 
                  With deep expertise in neural networks and distributed computing, Michael leads 
                  our technical architecture and research initiatives.
                </p>
                <div className="flex flex-wrap gap-2 mt-4 justify-center">
                  <Badge variant="outline">Machine Learning</Badge>
                  <Badge variant="outline">System Architecture</Badge>
                  <Badge variant="outline">Neural Networks</Badge>
                </div>
              </CardContent>
            </Card>

            <Card className="group hover:shadow-lg transition-all duration-300">
              <CardContent className="p-8 text-center">
                <div className="w-24 h-24 bg-gradient-to-r from-accent-500 to-accent-600 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <span className="text-white font-bold text-2xl">RM</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Rick Mellenberger</h3>
                <p className="text-accent-600 font-medium mb-4">Full-Stack Innovator</p>
                <p className="text-muted-foreground">
                  Drives end-to-end software development and system integration. Rick combines 
                  frontend expertise with robust backend architecture to deliver seamless user 
                  experiences powered by intelligent automation.
                </p>
                <div className="flex flex-wrap gap-2 mt-4 justify-center">
                  <Badge variant="outline">Full-Stack Development</Badge>
                  <Badge variant="outline">System Integration</Badge>
                  <Badge variant="outline">UI/UX Design</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </div>
  );
};

export default About;
