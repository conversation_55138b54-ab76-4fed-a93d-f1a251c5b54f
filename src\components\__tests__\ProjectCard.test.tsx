import React from 'react';
import { render, screen } from '@testing-library/react';
import { ProjectCard } from '../ProjectCard';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

describe('ProjectCard', () => {
  const defaultProps = {
    title: 'Test Project',
    description: 'Test description',
    status: 'active' as const,
    type: 'catalyst' as const,
  };

  it('renders project title and description', () => {
    render(<ProjectCard {...defaultProps} />);
    
    expect(screen.getByText('Test Project')).toBeInTheDocument();
    expect(screen.getByText('Test description')).toBeInTheDocument();
  });

  it('renders status badge', () => {
    render(<ProjectCard {...defaultProps} />);
    
    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  it('renders explore button when URL is provided', () => {
    render(<ProjectCard {...defaultProps} url="https://example.com" />);
    
    const exploreButton = screen.getByRole('link', { name: /explore/i });
    expect(exploreButton).toBeInTheDocument();
    expect(exploreButton).toHaveAttribute('href', 'https://example.com');
  });

  it('renders coming soon button when no URL is provided', () => {
    render(<ProjectCard {...defaultProps} />);
    
    expect(screen.getByText('Coming Soon')).toBeInTheDocument();
  });

  it('applies vault styling for vault024 type', () => {
    render(<ProjectCard {...defaultProps} type="vault024" />);
    
    const card = screen.getByText('Test Project').closest('.vault-024-card');
    expect(card).toBeInTheDocument();
  });

  it('renders features when provided', () => {
    const features = ['Feature 1', 'Feature 2', 'Feature 3'];
    render(<ProjectCard {...defaultProps} features={features} />);
    
    expect(screen.getByText('Feature 1')).toBeInTheDocument();
    expect(screen.getByText('Feature 2')).toBeInTheDocument();
    expect(screen.getByText('Feature 3')).toBeInTheDocument();
  });

  it('shows +X more badge when more than 3 features', () => {
    const features = ['Feature 1', 'Feature 2', 'Feature 3', 'Feature 4', 'Feature 5'];
    render(<ProjectCard {...defaultProps} features={features} />);
    
    expect(screen.getByText('+2 more')).toBeInTheDocument();
  });
});
