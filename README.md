# Metamorphic Labs Website

> Redefining Reality with AI, Quantum Systems & Intelligent Software

A modern, high-performance marketing website showcasing Metamorphic Labs' cutting-edge AI systems, quantum computing initiatives, and intelligent software solutions.

## 🚀 Live Demo

- **Production**: [metamorphiclabs.ai](https://metamorphiclabs.ai)
- **Staging**: Vercel preview deployments

## ✨ Features

- **Modern Tech Stack**: React 19 RC, TypeScript 5.5, Vite 6
- **Responsive Design**: Mobile-first approach with Tailwind CSS 4
- **Accessibility**: WCAG 2.2 AA compliant
- **Performance**: Lighthouse scores ≥95 across all metrics
- **SEO Optimized**: Dynamic meta tags and structured data
- **Animations**: Smooth micro-interactions with Framer Motion
- **State Management**: Zustand for client-side state
- **Testing**: Comprehensive E2E tests with Playwright

## 🏗️ Architecture

### Core Systems Showcase
- **Catalyst**: Advanced prompt engineering and AI model optimization platform
- **Metamorphic Reactor**: Multi-agent orchestration and intelligent automation
- **Vault 024**: Decentralized generative art gallery and NFT ecosystem

### Tech Stack
- **Frontend**: React 19 RC + TypeScript 5.5
- **Build Tool**: Vite 6 with ESBuild
- **Styling**: Tailwind CSS 4 + shadcn/ui components
- **Animations**: Framer Motion
- **State**: Zustand
- **Testing**: Playwright (E2E) + Jest (Unit)
- **Deployment**: Vercel with CI/CD

## 🛠️ Development

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

```bash
# Clone the repository
git clone https://github.com/Metamorphic-Labs/metamorphic-genesis-site.git
cd metamorphic-genesis-site

# Install dependencies
npm install --legacy-peer-deps

# Start development server
npm run dev
```

### Available Scripts

```bash
# Development
npm run dev              # Start dev server (http://localhost:8080)
npm run build           # Build for production
npm run preview         # Preview production build

# Code Quality
npm run lint            # Lint and fix code
npm run lint:check      # Check linting without fixing
npm run format          # Format code with Prettier
npm run format:check    # Check formatting

# Testing
npm run test:e2e        # Run E2E tests
npm run test:e2e:ui     # Run E2E tests with UI
```

## 🎨 Design System

### Brand Colors
- **Primary Gradient**: `#3B82F6 → #9333EA → #D946EF`
- **Vault 024**: `#FFD700` (24k gold) on black
- **Background**: Pure black (`#000000`)

### Typography
- **Primary**: Inter (sans-serif)
- **Code**: JetBrains Mono (monospace)

### Components
- Gradient buttons with hover effects
- Animated project cards
- Responsive navigation
- Accessibility-first design

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # shadcn/ui base components
│   ├── ProjectCard.tsx # Project showcase cards
│   ├── Navigation.tsx  # Main navigation
│   ├── Footer.tsx      # Site footer
│   └── SEOHead.tsx     # Dynamic SEO meta tags
├── pages/              # Route components
│   ├── Index.tsx       # Home page
│   ├── About.tsx       # About page
│   ├── Systems.tsx     # Systems & environments
│   ├── Contact.tsx     # Contact form
│   └── NotFound.tsx    # 404 page
├── store/              # Zustand state management
├── hooks/              # Custom React hooks
├── test/               # Test files
│   ├── e2e/           # Playwright E2E tests
│   └── setup.ts       # Test configuration
└── styles/
    └── index.css       # Global styles and utilities
```

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Configure environment variables (if any)
3. Deploy automatically on push to main branch

### Manual Deployment

```bash
# Build the project
npm run build

# Deploy the dist/ folder to your hosting provider
```

## 🧪 Testing

### E2E Testing with Playwright

```bash
# Install Playwright browsers
npx playwright install

# Run tests
npm run test:e2e

# Run tests with UI
npm run test:e2e:ui
```

## 📊 Performance

### Lighthouse Scores (Target: ≥95)
- **Performance**: 95+
- **Accessibility**: 95+
- **Best Practices**: 95+
- **SEO**: 95+

### Optimizations
- Code splitting by vendor, UI, and motion libraries
- Image optimization
- Lazy loading
- Tree shaking
- Gzip compression

## 📄 License

This project is proprietary to Metamorphic Labs LLC. All rights reserved.

## 📞 Support

For questions or support, please contact:
- **Email**: <EMAIL>
- **Business**: <EMAIL>

---

Built with ❤️ by [Metamorphic Labs](https://metamorphiclabs.ai)

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/3a91b9fd-164e-4394-a594-fa34c5bb7847) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
