
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Metamorphic Labs - Black Theme (Default) */
    --background: 0 0% 0%; /* Pure black */
    --foreground: 0 0% 98%; /* Near white */
    --card: 0 0% 5%; /* Very dark gray */
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 5%;
    --popover-foreground: 0 0% 98%;
    --primary: 217 91% 60%; /* sky-500 */
    --primary-foreground: 0 0% 98%;
    --muted: 0 0% 10%; /* Dark gray */
    --muted-foreground: 0 0% 65%; /* Medium gray */
    --accent: 292 84% 61%; /* fuchsia-500 */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 15%; /* Dark border */
    --input: 0 0% 10%; /* Dark input background */
    --ring: 217 91% 60%; /* sky-500 for focus rings */
    --radius: 0.5rem;
  }

  .light {
    /* Light theme override (optional) */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 292 84% 61%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 217 91% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
}

@layer utilities {
  /* Metamorphic Labs Brand Gradients */
  .text-gradient {
    @apply bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent;
  }

  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent;
  }

  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary via-secondary to-accent;
  }

  .bg-gradient-hero {
    @apply bg-gradient-to-br from-black via-slate-900 to-slate-800;
  }

  /* Vault 024 Gold Styling */
  .vault-024 {
    @apply text-gold border-gold bg-black;
  }

  .vault-024-card {
    @apply bg-black border-gold text-gold hover:bg-gold hover:text-black transition-all duration-300;
  }

  .vault-024-text {
    @apply text-gold;
  }

  /* Holographic Effects */
  .holographic {
    @apply relative overflow-hidden;
  }

  .holographic::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent;
    transform: translateX(-100%);
    transition: transform 0.6s;
  }

  .holographic:hover::before {
    transform: translateX(100%);
  }

  /* Scroll Animations */
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
  }

  .animate-on-scroll.in-view {
    opacity: 1;
    transform: translateY(0);
  }

  /* Glow Effects */
  .glow-primary {
    @apply shadow-lg shadow-primary/25;
  }

  .glow-secondary {
    @apply shadow-lg shadow-secondary/25;
  }

  .glow-gold {
    @apply shadow-lg shadow-gold/25;
  }

  @media (prefers-reduced-motion: reduce) {
    .animate-on-scroll {
      animation: none;
      opacity: 1;
      transform: none;
    }

    .holographic::before {
      display: none;
    }
  }
}
