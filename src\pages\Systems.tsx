import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button-gradient';
import { ExternalLink, Zap, Brain, Palette, Server, Code, Database } from 'lucide-react';

const Systems = () => {
  const systems = [
    {
      id: 'catalyst',
      name: 'Catalyst',
      url: 'https://catalyst.metamorphiclabs.ai',
      status: 'Active',
      purpose: 'Advanced prompt engineering and AI model optimization platform',
      description: 'Catalyst empowers developers and researchers with cutting-edge prompt engineering tools, multi-model chaining capabilities, and intelligent optimization algorithms.',
      features: [
        'Multi-Model Orchestration',
        'Prompt Engineering Studio',
        'Performance Analytics',
        'Custom Training Pipelines',
        'API Integration Hub'
      ],
      icon: <Zap className="h-6 w-6" />,
      type: 'catalyst' as const,
      gradient: true
    },
    {
      id: 'reactor',
      name: 'Metamorphic Reactor',
      url: '#metamorphic-reactor',
      status: 'Development',
      purpose: 'Multi-agent orchestration and intelligent automation platform',
      description: 'The Reactor serves as the central nervous system for complex AI workflows, enabling seamless coordination between multiple AI agents and automated decision-making processes.',
      features: [
        'Multi-Agent Coordination',
        'Workflow Automation',
        'Real-time Monitoring',
        'Scalable Architecture',
        'Custom Agent Development'
      ],
      icon: <Brain className="h-6 w-6" />,
      type: 'reactor' as const,
      gradient: true
    },
    {
      id: 'vault024',
      name: 'Vault 024',
      url: 'https://vault024.metamorphiclabs.ai',
      status: 'Active',
      purpose: 'Decentralized generative art gallery and NFT ecosystem',
      description: 'Vault 024 represents the pinnacle of AI-generated art curation, featuring exclusive collections, artist collaborations, and blockchain-verified authenticity.',
      features: [
        'AI Art Generation',
        'NFT Marketplace',
        'Artist Collaborations',
        'Blockchain Verification',
        'Exclusive Collections'
      ],
      icon: <Palette className="h-6 w-6" />,
      type: 'vault024' as const,
      gradient: false
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  };

  return (
    <div className="min-h-screen bg-black py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Systems & <span className="text-gradient">Environments</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Explore our comprehensive ecosystem of AI platforms, tools, and environments 
            designed to revolutionize how you build, deploy, and scale intelligent systems.
          </p>
        </motion.div>

        {/* Systems Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid gap-8 md:grid-cols-2 lg:grid-cols-3"
        >
          {systems.map((system) => (
            <motion.div key={system.id} variants={itemVariants}>
              <Card className={`
                h-full transition-all duration-300 hover:shadow-2xl group
                ${system.type === 'vault024' 
                  ? 'vault-024-card border-2 hover:glow-gold' 
                  : 'bg-gradient-primary border-primary/20 hover:border-primary/40 hover:glow-primary'
                }
              `}>
                <CardHeader>
                  <div className="flex items-center justify-between mb-4">
                    <div className={`
                      p-3 rounded-lg
                      ${system.type === 'vault024' 
                        ? 'bg-gold/10 text-gold' 
                        : 'bg-white/10 text-white'
                      }
                    `}>
                      {system.icon}
                    </div>
                    <Badge 
                      className={
                        system.status === 'Active' 
                          ? 'bg-green-500 text-white' 
                          : 'bg-yellow-500 text-black'
                      }
                    >
                      {system.status}
                    </Badge>
                  </div>
                  <CardTitle className={`
                    text-xl mb-2
                    ${system.type === 'vault024' ? 'text-gold' : 'text-white'}
                  `}>
                    {system.name}
                  </CardTitle>
                  <p className={`
                    text-sm font-medium
                    ${system.type === 'vault024' ? 'text-gold/80' : 'text-white/80'}
                  `}>
                    {system.purpose}
                  </p>
                </CardHeader>
                <CardContent>
                  <p className={`
                    text-sm mb-4 leading-relaxed
                    ${system.type === 'vault024' ? 'text-gold/70' : 'text-white/70'}
                  `}>
                    {system.description}
                  </p>

                  {/* Features */}
                  <div className="mb-6">
                    <h4 className={`
                      text-sm font-semibold mb-2
                      ${system.type === 'vault024' ? 'text-gold' : 'text-white'}
                    `}>
                      Key Features
                    </h4>
                    <div className="flex flex-wrap gap-1">
                      {system.features.map((feature, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className={`
                            text-xs
                            ${system.type === 'vault024' 
                              ? 'border-gold/30 text-gold/70' 
                              : 'border-white/30 text-white/70'
                            }
                          `}
                        >
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Action Button */}
                  {system.url.startsWith('http') ? (
                    <Button
                      variant={system.type === 'vault024' ? 'vault' : 'gradient'}
                      className="w-full"
                      asChild
                    >
                      <a href={system.url} target="_blank" rel="noopener noreferrer">
                        <span>Explore {system.name}</span>
                        <ExternalLink className="ml-2 h-4 w-4" />
                      </a>
                    </Button>
                  ) : (
                    <Button
                      variant={system.type === 'vault024' ? 'vault-outline' : 'gradient-outline'}
                      className="w-full"
                      disabled
                    >
                      <span>Coming Soon</span>
                      <Server className="ml-2 h-4 w-4" />
                    </Button>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <Card className="bg-gradient-to-r from-slate-900 to-slate-800 border-slate-700">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold text-white mb-4">
                Need Custom Integration?
              </h3>
              <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
                Our systems are designed to work together seamlessly. Contact us to discuss 
                custom integrations, enterprise solutions, or specialized deployments.
              </p>
              <Button variant="gradient" size="lg" asChild>
                <a href="/contact">
                  <span>Get in Touch</span>
                  <ExternalLink className="ml-2 h-5 w-5" />
                </a>
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default Systems;
